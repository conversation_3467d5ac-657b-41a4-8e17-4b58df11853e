<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

class ExpoDevService
{
    /**
     * Get the Expo development URL with dynamic IP detection.
     */
    public static function getExpoDevUrl(): string
    {
        // Check if a specific URL is configured
        $configuredUrl = config('app.expo_dev_url');
        if ($configuredUrl) {
            return $configuredUrl;
        }

        // Dynamic IP detection
        $ip = self::detectLocalIp();
        $port = config('app.expo_dev_port', '8081');
        
        $url = "exp://{$ip}:{$port}";
        
        Log::info('Expo development URL detected', [
            'ip' => $ip,
            'port' => $port,
            'url' => $url
        ]);
        
        return $url;
    }

    /**
     * Detect the local IP address that the client is connecting from.
     */
    private static function detectLocalIp(): string
    {
        // Method 1: Try to get IP from the current request
        if (request() && request()->server('HTTP_X_FORWARDED_FOR')) {
            $forwarded = request()->server('HTTP_X_FORWARDED_FOR');
            $ips = explode(',', $forwarded);
            $clientIp = trim($ips[0]);
            
            if (self::isPrivateIp($clientIp)) {
                Log::info('Using client IP from X-Forwarded-For', ['ip' => $clientIp]);
                return $clientIp;
            }
        }

        // Method 2: Try to get IP from REMOTE_ADDR
        if (request() && request()->server('REMOTE_ADDR')) {
            $remoteAddr = request()->server('REMOTE_ADDR');
            
            if (self::isPrivateIp($remoteAddr)) {
                Log::info('Using client IP from REMOTE_ADDR', ['ip' => $remoteAddr]);
                return $remoteAddr;
            }
        }

        // Method 3: Try to detect server's local IP
        $serverIp = self::detectServerLocalIp();
        if ($serverIp) {
            Log::info('Using detected server IP', ['ip' => $serverIp]);
            return $serverIp;
        }

        // Method 4: Fallback to common development IP
        $fallbackIp = '*************';
        Log::warning('Using fallback IP address', ['ip' => $fallbackIp]);
        return $fallbackIp;
    }

    /**
     * Detect the server's local IP address.
     */
    private static function detectServerLocalIp(): ?string
    {
        // Try different methods to get local IP
        $methods = [
            // Method 1: Use hostname -I (Linux/Mac)
            function() {
                $output = shell_exec('hostname -I 2>/dev/null');
                if ($output) {
                    $ips = explode(' ', trim($output));
                    foreach ($ips as $ip) {
                        $ip = trim($ip);
                        if (self::isPrivateIp($ip)) {
                            return $ip;
                        }
                    }
                }
                return null;
            },
            
            // Method 2: Use ifconfig (Mac/Linux)
            function() {
                $output = shell_exec("ifconfig 2>/dev/null | grep 'inet ' | grep -v '127.0.0.1' | awk '{print $2}' | head -1");
                if ($output) {
                    $ip = trim(str_replace('addr:', '', $output));
                    if (self::isPrivateIp($ip)) {
                        return $ip;
                    }
                }
                return null;
            },
            
            // Method 3: Use ip route (Linux)
            function() {
                $output = shell_exec("ip route get ******* 2>/dev/null | awk '{print $7}' | head -1");
                if ($output) {
                    $ip = trim($output);
                    if (self::isPrivateIp($ip)) {
                        return $ip;
                    }
                }
                return null;
            }
        ];

        foreach ($methods as $method) {
            try {
                $result = $method();
                if ($result) {
                    return $result;
                }
            } catch (\Exception $e) {
                // Continue to next method
                continue;
            }
        }

        return null;
    }

    /**
     * Check if an IP address is a private/local IP.
     */
    private static function isPrivateIp(string $ip): bool
    {
        if (!filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
            return false;
        }

        // Check for private IP ranges
        return (
            // 10.0.0.0/8
            (ip2long($ip) >= ip2long('10.0.0.0') && ip2long($ip) <= ip2long('**************')) ||
            // **********/12
            (ip2long($ip) >= ip2long('**********') && ip2long($ip) <= ip2long('**************')) ||
            // ***********/16
            (ip2long($ip) >= ip2long('***********') && ip2long($ip) <= ip2long('***************'))
        );
    }

    /**
     * Build the complete Expo development redirect URL.
     */
    public static function buildRedirectUrl(string $path, array $params = []): string
    {
        $baseUrl = self::getExpoDevUrl();
        $queryString = http_build_query($params);
        
        return $baseUrl . '/--' . $path . ($queryString ? '?' . $queryString : '');
    }
}
