<?php

namespace Tests\Feature;

use App\Models\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Tests\TestCase;

class DeepLinkAuthTest extends TestCase
{
    use RefreshDatabase;

    public function test_callback_creates_short_lived_token_and_redirects_to_deep_link()
    {
        // Set up configuration
        config([
            'services.okta.issuer' => 'https://test.okta.com/oauth2/default',
            'services.okta.client_id' => 'test_client_id',
            'services.okta.client_secret' => 'test_client_secret',
            'services.okta.redirect_uri' => 'http://localhost/sso-auth/callback',
            'app.scheme' => 'testapp',
            'app.auth_callback_path' => 'auth-callback',
            'app.expo_dev_mode' => false, // Explicitly disable Expo dev mode for tests
            'app.token_signing_key' => 'base64:' . base64_encode('test-signing-key-32-characters-long'),
        ]);

        // Set up session data
        Session::put([
            'oauth_state' => 'test-state',
            'code_verifier' => 'test-verifier',
            'auth_platform' => 'mobile'
        ]);

        // Mock Okta token exchange
        Http::fake([
            'https://test.okta.com/oauth2/default/v1/token' => Http::response([
                'access_token' => 'test-access-token',
                'id_token' => 'test-id-token',
                'refresh_token' => 'test-refresh-token',
                'expires_in' => 3600,
            ]),
            'https://test.okta.com/oauth2/default/v1/userinfo' => Http::response([
                'sub' => 'test-user-id',
                'email' => '<EMAIL>',
                'name' => 'Test User',
            ]),
        ]);

        // Make callback request
        $response = $this->get('/sso-auth/callback?code=test-code&state=test-state');

        // Assert redirect to deep link
        $response->assertStatus(302);
        $this->assertStringStartsWith('testapp://auth-callback?token=', $response->headers->get('Location'));

        // Verify token can be decoded
        $location = $response->headers->get('Location');
        parse_str(parse_url($location, PHP_URL_QUERY), $params);
        
        $this->assertArrayHasKey('token', $params);
        $this->assertArrayHasKey('user', $params);

        // Decode and verify JWT token
        $token = urldecode($params['token']);
        $key = base64_decode(config('app.token_signing_key'));
        $decoded = JWT::decode($token, new Key($key, 'HS256'));

        $this->assertEquals('test-user-id', $decoded->sub);
        $this->assertEquals('<EMAIL>', $decoded->email);
        $this->assertEquals('Test User', $decoded->name);
        
        // Verify token expires in ~60 seconds
        $this->assertLessThanOrEqual(time() + 61, $decoded->exp);
        $this->assertGreaterThanOrEqual(time() + 59, $decoded->exp);
    }

    public function test_callback_handles_missing_code_error()
    {
        config([
            'app.scheme' => 'testapp',
            'app.auth_callback_path' => 'auth-callback',
            'app.expo_dev_mode' => false // Explicitly disable Expo dev mode for tests
        ]);

        Session::put('oauth_state', 'test-state');

        $response = $this->get('/sso-auth/callback?state=test-state');

        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringStartsWith('testapp://auth-callback?error=', $location);
    }

    public function test_callback_handles_invalid_state_error()
    {
        config([
            'app.scheme' => 'testapp',
            'app.auth_callback_path' => 'auth-callback',
            'app.expo_dev_mode' => false // Explicitly disable Expo dev mode for tests
        ]);

        Session::put('oauth_state', 'correct-state');

        $response = $this->get('/sso-auth/callback?code=test-code&state=wrong-state');

        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringStartsWith('testapp://auth-callback?error=', $location);
    }

    public function test_callback_uses_expo_dev_url_when_expo_dev_mode_enabled()
    {
        config([
            'app.scheme' => 'testapp',
            'app.auth_callback_path' => 'auth-callback',
            'app.expo_dev_mode' => true, // Enable Expo dev mode
            'app.expo_dev_url' => 'exp://192.168.1.100:8081' // Set a specific URL for testing
        ]);

        Session::put('oauth_state', 'test-state');

        $response = $this->get('/sso-auth/callback?state=test-state');

        $response->assertStatus(302);
        $location = $response->headers->get('Location');
        $this->assertStringStartsWith('exp://192.168.1.100:8081/--/auth-callback?error=', $location);
    }
}
